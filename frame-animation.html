<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帧序列滚动动画</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
        }

        .container {
            height: 600vh; /* 更长的滚动区域以展示更多帧 */
        }

        .sticky-container {
            position: sticky;
            top: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
            background: #000;
        }

        .frame-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transition: opacity 0.1s ease-out; /* 快速切换模拟帧动画 */
        }

        .frame.active {
            opacity: 1;
        }

        .content {
            position: absolute;
            bottom: 50px;
            left: 50px;
            z-index: 10;
            background: rgba(0, 0, 0, 0.7);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
        }

        h1 {
            font-size: 2.5rem;
            margin: 0 0 15px 0;
            font-weight: 700;
        }

        p {
            font-size: 1.1rem;
            margin: 0;
            opacity: 0.9;
            line-height: 1.5;
        }

        .frame-info {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px 20px;
            border-radius: 10px;
            font-family: 'Monaco', monospace;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .instructions {
            position: absolute;
            top: 30px;
            left: 30px;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
            max-width: 300px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .instructions h3 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
            color: #4a90e2;
        }

        .instructions p {
            margin: 5px 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .content {
                bottom: 30px;
                left: 20px;
                right: 20px;
                max-width: none;
                padding: 20px;
            }
            
            .frame-info {
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                font-size: 0.8rem;
            }
            
            .instructions {
                top: 20px;
                left: 20px;
                padding: 15px;
                max-width: 250px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sticky-container">
            <div class="instructions">
                <h3>使用说明</h3>
                <p>• 向下滚动查看帧动画</p>
                <p>• 当前使用占位图片</p>
                <p>• 替换为您的图片序列</p>
                <p>• 建议使用连续的帧图片</p>
            </div>
            
            <div class="frame-info" id="frameInfo">
                帧: <span id="currentFrame">1</span> / <span id="totalFrames">24</span>
            </div>
            
            <div class="frame-container" id="frameContainer">
                <!-- 帧将通过 JavaScript 动态添加 -->
            </div>
            
            <div class="content">
                <h1 id="title">帧序列动画</h1>
                <p id="description">使用连续图片序列创建流畅的滚动动画效果</p>
            </div>
        </div>
    </div>

    <script>
        // 配置参数
        const frameCount = 24; // 24帧，模拟1秒的动画（24fps）
        
        // 生成图片序列 - 您需要替换为实际的图片路径
        // 格式示例: frame_001.jpg, frame_002.jpg, frame_003.jpg...
        function generateImageUrls() {
            const urls = [];
            for (let i = 1; i <= frameCount; i++) {
                // 使用占位图片，您需要替换为实际的帧序列图片
                const frameNumber = i.toString().padStart(3, '0');
                // urls.push(`./images/frame_${frameNumber}.jpg`); // 实际使用时的格式
                
                // 临时使用不同的占位图片来演示效果
                const placeholderUrls = [
                    'https://picsum.photos/1920/1080?random=1',
                    'https://picsum.photos/1920/1080?random=2',
                    'https://picsum.photos/1920/1080?random=3',
                    'https://picsum.photos/1920/1080?random=4',
                    'https://picsum.photos/1920/1080?random=5',
                    'https://picsum.photos/1920/1080?random=6'
                ];
                urls.push(placeholderUrls[i % placeholderUrls.length]);
            }
            return urls;
        }
        
        const imageUrls = generateImageUrls();

        // 获取DOM元素
        const frameContainer = document.getElementById('frameContainer');
        const title = document.getElementById('title');
        const description = document.getElementById('description');
        const currentFrameSpan = document.getElementById('currentFrame');
        const totalFramesSpan = document.getElementById('totalFrames');

        // 设置总帧数显示
        totalFramesSpan.textContent = frameCount;

        // 创建图片帧
        function createFrames() {
            for (let i = 0; i < frameCount; i++) {
                const frame = document.createElement('div');
                frame.className = 'frame';
                frame.style.backgroundImage = `url(${imageUrls[i]})`;
                frame.dataset.index = i;
                frameContainer.appendChild(frame);
            }
        }

        // 预加载图片
        function preloadImages() {
            imageUrls.forEach((url, index) => {
                const img = new Image();
                img.onload = () => {
                    console.log(`帧 ${index + 1} 加载完成`);
                };
                img.src = url;
            });
        }

        // 主滚动处理函数
        function handleScroll() {
            const scrollTop = window.scrollY;
            const container = document.querySelector('.container');
            const maxScroll = container.offsetHeight - window.innerHeight;
            const scrollFraction = Math.min(scrollTop / maxScroll, 1);
            
            // 计算当前帧（更精确的帧计算）
            const frameIndex = Math.min(
                frameCount - 1,
                Math.floor(scrollFraction * frameCount)
            );
            
            // 更新帧显示
            updateFrames(frameIndex);
            
            // 更新帧信息显示
            currentFrameSpan.textContent = frameIndex + 1;
            
            // 更新内容
            updateContent(frameIndex);
        }

        // 更新帧显示
        function updateFrames(currentIndex) {
            const frames = document.querySelectorAll('.frame');
            
            frames.forEach((frame, index) => {
                if (index === currentIndex) {
                    frame.classList.add('active');
                } else {
                    frame.classList.remove('active');
                }
            });
        }

        // 更新内容
        function updateContent(frameIndex) {
            const progress = (frameIndex + 1) / frameCount;
            const percentage = Math.round(progress * 100);
            
            title.textContent = `帧序列动画 ${percentage}%`;
            description.textContent = `当前显示第 ${frameIndex + 1} 帧，共 ${frameCount} 帧`;
        }

        // 初始化
        function init() {
            console.log('开始初始化帧序列动画...');
            
            // 预加载图片
            preloadImages();
            
            // 创建帧
            createFrames();
            
            // 设置初始状态
            updateFrames(0);
            updateContent(0);
            
            // 绑定滚动事件
            window.addEventListener('scroll', handleScroll, { passive: true });
            
            console.log(`帧序列动画初始化完成，共 ${frameCount} 帧`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
