<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动动画 - 真实图片版本</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
        }

        .container {
            height: 500vh; /* 滚动区域高度 */
        }

        .sticky-container {
            position: sticky;
            top: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        .frame-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .frame.active {
            opacity: 1;
            transform: scale(1);
        }

        .content {
            position: absolute;
            bottom: 50px;
            left: 50px;
            z-index: 10;
            background: rgba(0, 0, 0, 0.6);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 400px;
        }

        h1 {
            font-size: 2.5rem;
            margin: 0 0 15px 0;
            font-weight: 700;
        }

        p {
            font-size: 1.1rem;
            margin: 0;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .content {
                bottom: 30px;
                left: 20px;
                right: 20px;
                max-width: none;
                padding: 20px;
            }

            h1 {
                font-size: 2rem;
            }

            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sticky-container">
            <div class="frame-container" id="frameContainer">
                <!-- 动画帧将通过 JavaScript 动态添加 -->
            </div>

            <div class="content">
                <h1 id="title">滚动动画演示</h1>
                <p id="description">向下滚动查看图片切换效果</p>
            </div>
        </div>
    </div>

    <script>
        // 配置参数
        const frameCount = 10; // 图片数量

        // 图片URL数组 - 您可以替换为自己的图片
        const imageUrls = [
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1447752875215-b2761acb3c5d?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1418065460487-3d7ee9be9d70?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1501436513145-30f24e19fcc4?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop',
            'https://images.unsplash.com/photo-1475924156734-496f6cac6ec1?w=1920&h=1080&fit=crop'
        ];

        // 对应的标题和描述
        const contentData = [
            { title: "壮丽山景", desc: "感受大自然的雄伟壮观" },
            { title: "森林秘境", desc: "探索神秘的绿色世界" },
            { title: "宁静湖泊", desc: "享受平静致远的美好时光" },
            { title: "雪山之巅", desc: "征服世界之巅的豪迈" },
            { title: "日出东方", desc: "迎接新一天的希望之光" },
            { title: "田园风光", desc: "回归简单纯朴的生活" },
            { title: "海岸线", desc: "聆听海浪的永恒旋律" },
            { title: "星空银河", desc: "仰望浩瀚无垠的宇宙" },
            { title: "城市夜景", desc: "感受现代都市的繁华" },
            { title: "完美结束", desc: "感谢您的观看体验" }
        ];

        // 获取DOM元素
        const frameContainer = document.getElementById('frameContainer');
        const title = document.getElementById('title');
        const description = document.getElementById('description');

        // 创建图片帧
        function createFrames() {
            for (let i = 0; i < frameCount; i++) {
                const frame = document.createElement('div');
                frame.className = 'frame';
                frame.style.backgroundImage = `url(${imageUrls[i]})`;
                frame.dataset.index = i;
                frameContainer.appendChild(frame);
            }
        }

        // 预加载图片
        function preloadImages() {
            imageUrls.forEach(url => {
                const img = new Image();
                img.src = url;
            });
        }

        // 主滚动处理函数
        function handleScroll() {
            const scrollTop = window.scrollY;
            const container = document.querySelector('.container');
            const maxScroll = container.offsetHeight - window.innerHeight;
            const scrollFraction = Math.min(scrollTop / maxScroll, 1);

            // 计算当前帧
            const frameIndex = Math.min(
                frameCount - 1,
                Math.floor(scrollFraction * frameCount)
            );

            // 更新帧显示
            updateFrames(frameIndex);

            // 更新内容
            updateContent(frameIndex);
        }

        // 更新帧显示
        function updateFrames(currentIndex) {
            const frames = document.querySelectorAll('.frame');

            frames.forEach((frame, index) => {
                if (index === currentIndex) {
                    frame.classList.add('active');
                } else {
                    frame.classList.remove('active');
                }
            });
        }

        // 更新内容
        function updateContent(frameIndex) {
            const data = contentData[frameIndex];
            if (data) {
                title.textContent = data.title;
                description.textContent = data.desc;
            }
        }

        // 初始化
        function init() {
            // 预加载图片
            preloadImages();

            // 创建帧
            createFrames();

            // 设置初始状态
            updateFrames(0);
            updateContent(0);

            // 绑定滚动事件
            window.addEventListener('scroll', handleScroll, { passive: true });

            console.log('滚动动画已初始化');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
