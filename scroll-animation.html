<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动动画 - 真实图片版本</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
        }

        .container {
            height: 500vh; /* 滚动区域高度 */
        }

        .sticky-container {
            position: sticky;
            top: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        .frame-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .frame.active {
            opacity: 1;
            transform: scale(1);
        }

        .content {
            position: absolute;
            bottom: 50px;
            left: 50px;
            z-index: 10;
            background: rgba(0, 0, 0, 0.6);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 400px;
        }

        h1 {
            font-size: 2.5rem;
            margin: 0 0 15px 0;
            font-weight: 700;
        }

        p {
            font-size: 1.1rem;
            margin: 0;
            opacity: 0.9;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .content {
                bottom: 30px;
                left: 20px;
                right: 20px;
                max-width: none;
                padding: 20px;
            }

            h1 {
                font-size: 2rem;
            }

            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sticky-container">
            <div class="frame-container" id="frameContainer">
                <!-- 动画帧将通过 JavaScript 动态添加 -->
            </div>

            <div class="content">
                <h1 id="title">滚动动画演示</h1>
                <p id="description">向下滚动查看图片切换效果</p>
            </div>
        </div>
    </div>

    <script>
        // 配置参数
        const frameCount = 15; // 帧数量

        // 统一主题的图片序列 - 日出到日落的时间变化
        const imageUrls = [
            // 深夜到黎明 (帧 1-3)
            'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop', // 深夜星空
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop', // 黎明前
            'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=1920&h=1080&fit=crop', // 晨曦初现

            // 日出时分 (帧 4-6)
            'https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=1920&h=1080&fit=crop', // 日出开始
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop', // 日出进行
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop', // 日出完成

            // 上午时光 (帧 7-9)
            'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1920&h=1080&fit=crop', // 清晨森林
            'https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=1920&h=1080&fit=crop', // 上午山景
            'https://images.unsplash.com/photo-1447752875215-b2761acb3c5d?w=1920&h=1080&fit=crop', // 明亮天空

            // 正午时分 (帧 10-12)
            'https://images.unsplash.com/photo-1418065460487-3d7ee9be9d70?w=1920&h=1080&fit=crop', // 正午阳光
            'https://images.unsplash.com/photo-1501436513145-30f24e19fcc4?w=1920&h=1080&fit=crop', // 午后时光
            'https://images.unsplash.com/photo-1475924156734-496f6cac6ec1?w=1920&h=1080&fit=crop', // 下午景色

            // 黄昏到夜晚 (帧 13-15)
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop', // 黄昏时分
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1920&h=1080&fit=crop', // 日落西山
            'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop'  // 夜幕降临
        ];

        // 对应一天时间变化的标题和描述
        const contentData = [
            { title: "深夜星空", desc: "繁星点点，宁静致远的夜晚时光" },
            { title: "黎明前夕", desc: "黑暗即将过去，光明即将到来" },
            { title: "晨曦初现", desc: "第一缕阳光穿透云层" },
            { title: "日出东方", desc: "太阳缓缓升起，新的一天开始" },
            { title: "朝阳满天", desc: "金色的阳光洒向大地" },
            { title: "晨光熹微", desc: "温暖的晨光唤醒沉睡的世界" },
            { title: "清晨森林", desc: "鸟语花香，生机勃勃的早晨" },
            { title: "上午时光", desc: "阳光明媚，万物生长的美好时刻" },
            { title: "蓝天白云", desc: "清澈的天空，自由飘荡的云朵" },
            { title: "正午阳光", desc: "炽热的阳光，充满活力的正午" },
            { title: "午后时光", desc: "温暖惬意的下午时光" },
            { title: "傍晚景色", desc: "夕阳西下前的最后光辉" },
            { title: "黄昏时分", desc: "天空染上金黄色的浪漫时刻" },
            { title: "日落西山", desc: "太阳缓缓落下，一天即将结束" },
            { title: "夜幕降临", desc: "星空再现，新的轮回即将开始" }
        ];

        // 获取DOM元素
        const frameContainer = document.getElementById('frameContainer');
        const title = document.getElementById('title');
        const description = document.getElementById('description');

        // 创建图片帧
        function createFrames() {
            for (let i = 0; i < frameCount; i++) {
                const frame = document.createElement('div');
                frame.className = 'frame';
                frame.style.backgroundImage = `url(${imageUrls[i]})`;
                frame.dataset.index = i;
                frameContainer.appendChild(frame);
            }
        }

        // 预加载图片
        function preloadImages() {
            imageUrls.forEach(url => {
                const img = new Image();
                img.src = url;
            });
        }

        // 主滚动处理函数
        function handleScroll() {
            const scrollTop = window.scrollY;
            const container = document.querySelector('.container');
            const maxScroll = container.offsetHeight - window.innerHeight;
            const scrollFraction = Math.min(scrollTop / maxScroll, 1);

            // 计算当前帧
            const frameIndex = Math.min(
                frameCount - 1,
                Math.floor(scrollFraction * frameCount)
            );

            // 更新帧显示
            updateFrames(frameIndex);

            // 更新内容
            updateContent(frameIndex);
        }

        // 更新帧显示
        function updateFrames(currentIndex) {
            const frames = document.querySelectorAll('.frame');

            frames.forEach((frame, index) => {
                if (index === currentIndex) {
                    frame.classList.add('active');
                } else {
                    frame.classList.remove('active');
                }
            });
        }

        // 更新内容
        function updateContent(frameIndex) {
            const data = contentData[frameIndex];
            if (data) {
                title.textContent = data.title;
                description.textContent = data.desc;
            }
        }

        // 初始化
        function init() {
            // 预加载图片
            preloadImages();

            // 创建帧
            createFrames();

            // 设置初始状态
            updateFrames(0);
            updateContent(0);

            // 绑定滚动事件
            window.addEventListener('scroll', handleScroll, { passive: true });

            console.log('滚动动画已初始化');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
