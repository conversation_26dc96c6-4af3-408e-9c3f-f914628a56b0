<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级滚动动画效果 - 灵感来自Adaline.ai</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #50e3c2;
            --accent-color: #ff6b6b;
            --text-color: #ffffff;
            --bg-color: #000000;
            --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--bg-color) 0%, #1a1a2e 50%, #16213e 100%);
            color: var(--text-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .container {
            height: 800vh; /* 增加滚动区域以展示更多效果 */
            position: relative;
        }

        .sticky-container {
            position: sticky;
            top: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .frame-container {
            position: relative;
            width: 100%;
            height: 100%;
            perspective: 1000px;
        }

        .frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            opacity: 0;
            transform: scale(0.8) rotateY(15deg);
            transition: all 0.6s var(--transition-smooth);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .frame.active {
            opacity: 1;
            transform: scale(1) rotateY(0deg);
            box-shadow: 0 30px 80px rgba(74, 144, 226, 0.2);
        }

        .frame.next {
            opacity: 0.3;
            transform: scale(0.9) rotateY(-10deg) translateX(50px);
        }

        .frame.prev {
            opacity: 0.3;
            transform: scale(0.9) rotateY(10deg) translateX(-50px);
        }

        .content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        h1 {
            font-size: clamp(2rem, 5vw, 4rem);
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            letter-spacing: -0.02em;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { filter: drop-shadow(0 0 10px rgba(74, 144, 226, 0.3)); }
            100% { filter: drop-shadow(0 0 20px rgba(80, 227, 194, 0.5)); }
        }

        p {
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            line-height: 1.6;
            margin: 0;
            opacity: 0.9;
            font-weight: 300;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            z-index: 1000;
            transition: width 0.1s ease;
            box-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
        }

        .frame-indicator {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s var(--transition-smooth);
            cursor: pointer;
        }

        .indicator-dot.active {
            background: var(--primary-color);
            transform: scale(1.3);
            box-shadow: 0 0 15px rgba(74, 144, 226, 0.6);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            width: 6px;
            height: 6px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .performance-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 0.8rem;
            font-family: 'Monaco', monospace;
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- 进度条 -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- 帧指示器 -->
    <div class="frame-indicator" id="frameIndicator"></div>

    <!-- 性能信息 -->
    <div class="performance-info" id="performanceInfo">
        FPS: <span id="fps">60</span> | Frame: <span id="currentFrame">1</span>/<span id="totalFrames">20</span>
    </div>

    <div class="container">
        <div class="sticky-container">
            <!-- 浮动元素 -->
            <div class="floating-elements" id="floatingElements"></div>

            <div class="frame-container" id="frameContainer">
                <!-- 动画帧将通过 JavaScript 动态添加 -->
            </div>

            <div class="content" id="content">
                <h1 id="title">高级滚动动画演示</h1>
                <p id="description">体验流畅的3D变换和视觉效果，灵感来自现代Web设计</p>
            </div>
        </div>
    </div>

    <script>
        // 配置参数
        const config = {
            frameCount: 20,
            floatingElementsCount: 15,
            performanceMonitoring: true,
            smoothScrolling: true,
            parallaxEffect: true
        };

        // 性能监控
        let fps = 0;
        let lastTime = performance.now();
        let frameCounter = 0;

        // 获取DOM元素
        const frameContainer = document.getElementById('frameContainer');
        const progressBar = document.getElementById('progressBar');
        const frameIndicator = document.getElementById('frameIndicator');
        const floatingElements = document.getElementById('floatingElements');
        const content = document.getElementById('content');
        const title = document.getElementById('title');
        const description = document.getElementById('description');

        // 创建高质量渐变背景
        const gradients = [
            'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
            'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
            'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
            'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
            'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)',
            'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
            'linear-gradient(135deg, #ff8a80 0%, #ea80fc 100%)',
            'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)',
            'linear-gradient(135deg, #cfd9df 0%, #e2ebf0 100%)',
            'linear-gradient(135deg, #a8caba 0%, #5d4e75 100%)',
            'linear-gradient(135deg, #b2fefa 0%, #0ed2f7 100%)',
            'linear-gradient(135deg, #f6d365 0%, #fda085 100%)',
            'linear-gradient(135deg, #96fbc4 0%, #f9f586 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        ];

        // 内容数据
        const contentData = [
            { title: "欢迎体验", desc: "现代化的滚动动画效果" },
            { title: "流畅过渡", desc: "基于CSS3和JavaScript的高性能动画" },
            { title: "3D变换", desc: "立体视觉效果增强用户体验" },
            { title: "响应式设计", desc: "适配各种屏幕尺寸和设备" },
            { title: "性能优化", desc: "60FPS流畅动画，GPU加速渲染" },
            { title: "视觉层次", desc: "深度感知和空间布局设计" },
            { title: "交互反馈", desc: "实时进度指示和状态反馈" },
            { title: "现代美学", desc: "简洁优雅的视觉语言" },
            { title: "技术创新", desc: "前沿Web技术的完美结合" },
            { title: "用户体验", desc: "直观自然的交互方式" },
            { title: "品质保证", desc: "精心打磨的每一个细节" },
            { title: "跨平台", desc: "兼容主流浏览器和设备" },
            { title: "可扩展性", desc: "模块化设计便于定制" },
            { title: "性能监控", desc: "实时性能数据可视化" },
            { title: "动画库", desc: "丰富的动画效果组合" },
            { title: "设计系统", desc: "统一的视觉设计规范" },
            { title: "开发效率", desc: "快速实现复杂动画效果" },
            { title: "维护性", desc: "清晰的代码结构和注释" },
            { title: "未来展望", desc: "持续演进的技术方案" },
            { title: "完美收官", desc: "感谢您的观看和体验" }
        ];

        // 创建帧元素
        function createFrames() {
            for (let i = 0; i < config.frameCount; i++) {
                const frame = document.createElement('div');
                frame.className = 'frame';
                frame.style.background = gradients[i];
                frame.dataset.index = i;
                frameContainer.appendChild(frame);

                // 创建指示器点
                const dot = document.createElement('div');
                dot.className = 'indicator-dot';
                dot.dataset.index = i;
                dot.addEventListener('click', () => scrollToFrame(i));
                frameIndicator.appendChild(dot);
            }
        }

        // 创建浮动元素
        function createFloatingElements() {
            for (let i = 0; i < config.floatingElementsCount; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (4 + Math.random() * 4) + 's';
                floatingElements.appendChild(element);
            }
        }

        // 滚动到指定帧
        function scrollToFrame(frameIndex) {
            const container = document.querySelector('.container');
            const targetScroll = (frameIndex / (config.frameCount - 1)) *
                                (container.offsetHeight - window.innerHeight);
            window.scrollTo({
                top: targetScroll,
                behavior: 'smooth'
            });
        }

        // 更新性能信息
        function updatePerformance() {
            if (!config.performanceMonitoring) return;

            const currentTime = performance.now();
            frameCounter++;

            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCounter * 1000) / (currentTime - lastTime));
                document.getElementById('fps').textContent = fps;
                frameCounter = 0;
                lastTime = currentTime;
            }
        }

        // 主滚动处理函数
        function handleScroll() {
            requestAnimationFrame(() => {
                updatePerformance();

                const scrollTop = window.scrollY;
                const container = document.querySelector('.container');
                const maxScroll = container.offsetHeight - window.innerHeight;
                const scrollFraction = Math.min(scrollTop / maxScroll, 1);

                // 更新进度条
                progressBar.style.width = (scrollFraction * 100) + '%';

                // 计算当前帧
                const frameIndex = Math.min(
                    config.frameCount - 1,
                    Math.floor(scrollFraction * config.frameCount)
                );

                // 更新性能信息中的当前帧
                document.getElementById('currentFrame').textContent = frameIndex + 1;
                document.getElementById('totalFrames').textContent = config.frameCount;

                // 更新帧显示
                updateFrames(frameIndex, scrollFraction);

                // 更新指示器
                updateIndicators(frameIndex);

                // 更新内容
                updateContent(frameIndex);

                // 视差效果
                if (config.parallaxEffect) {
                    const parallaxOffset = scrollTop * 0.5;
                    floatingElements.style.transform = `translateY(${parallaxOffset}px)`;
                }
            });
        }

        // 更新帧显示
        function updateFrames(currentIndex, scrollFraction) {
            const frames = document.querySelectorAll('.frame');

            frames.forEach((frame, index) => {
                frame.classList.remove('active', 'next', 'prev');

                if (index === currentIndex) {
                    frame.classList.add('active');
                } else if (index === currentIndex + 1) {
                    frame.classList.add('next');
                } else if (index === currentIndex - 1) {
                    frame.classList.add('prev');
                }
            });
        }

        // 更新指示器
        function updateIndicators(currentIndex) {
            const dots = document.querySelectorAll('.indicator-dot');
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentIndex);
            });
        }

        // 更新内容
        function updateContent(frameIndex) {
            const data = contentData[frameIndex];
            if (data) {
                title.textContent = data.title;
                description.textContent = data.desc;
            }
        }

        // 初始化
        function init() {
            createFrames();
            createFloatingElements();

            // 设置初始状态
            updateFrames(0, 0);
            updateIndicators(0);
            updateContent(0);

            // 绑定事件
            window.addEventListener('scroll', handleScroll, { passive: true });

            // 初始化性能监控
            if (config.performanceMonitoring) {
                setInterval(updatePerformance, 16); // ~60fps
            }

            console.log('高级滚动动画已初始化');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
