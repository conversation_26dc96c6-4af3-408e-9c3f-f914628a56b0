<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动动画效果</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }

        .container {
            height: 500vh; /* 设置较长的滚动区域 */
        }

        .sticky-container {
            position: sticky;
            top: 0;
            height: 100vh;
            width: 100%;
            overflow: hidden;
        }

        .frame-container {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .frame {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .content {
            position: relative;
            z-index: 2;
            padding: 20px;
            text-align: center;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        h1 {
            font-size: 3em;
            margin-bottom: 20px;
        }

        p {
            font-size: 1.5em;
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sticky-container">
            <div class="frame-container" id="frameContainer">
                <!-- 动画帧将通过 JavaScript 动态添加 -->
            </div>
            <div class="content">
                <h1>滚动动画演示</h1>
                <p>向下滚动以查看动画效果</p>
            </div>
        </div>
    </div>

    <script>
        const frameCount = 10; // 总帧数
        const frameContainer = document.getElementById('frameContainer');
        
        // 创建帧元素
        for (let i = 1; i <= frameCount; i++) {
            const frame = document.createElement('div');
            frame.className = 'frame';
            // 这里使用渐变作为示例，实际应用中可替换为真实图片
            frame.style.background = `linear-gradient(${360 / frameCount * i}deg, #4a90e2, #50e3c2)`;
            frameContainer.appendChild(frame);
        }

        const frames = document.querySelectorAll('.frame');
        
        // 监听滚动事件
        window.addEventListener('scroll', () => {
            const scrollTop = window.scrollY;
            const maxScroll = document.querySelector('.container').offsetHeight - window.innerHeight;
            const scrollFraction = scrollTop / maxScroll;
            const frameIndex = Math.min(
                frameCount - 1,
                Math.floor(scrollFraction * frameCount)
            );

            // 更新帧的可见性
            frames.forEach((frame, index) => {
                frame.style.opacity = index === frameIndex ? 1 : 0;
            });
        });
    </script>
</body>
</html>
