<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景逐帧滚动动画</title>
    <style>
        /* 重置默认样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 页面基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f172a; /* 深色背景作为基础色 */
            color: #f8fafc;
            line-height: 1.6;
            overflow-x: hidden; /* 隐藏水平滚动条 */
            position: relative;
        }

        /* 固定背景容器 - 用于承载所有背景帧 */
        .animated-background {
            position: fixed; /* 固定定位，不随页面滚动 */
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh; /* 全屏高度 */
            z-index: -1; /* 置于最底层 */
            background: #0f172a; /* 基础背景色 */
            overflow: hidden; /* 隐藏溢出内容 */
        }

        /* 背景帧基础样式 - 每个背景帧的通用样式 */
        .bg-layer {
            position: absolute; /* 绝对定位，重叠放置 */
            width: 100%;
            height: 100%;
            opacity: 0; /* 默认透明，通过JS控制显示 */
            transition: opacity 0.5s ease; /* 平滑的透明度过渡效果 */
        }

        /* 激活状态的背景帧 - 当前显示的帧 */
        .bg-layer.active {
            opacity: 1; /* 完全不透明 */
        }

        /* 背景帧1 - 左上角紫色光晕 + 右下角紫色光晕 */
        .bg-frame-1 {
            background: 
                radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.15) 0%, transparent 60%),
                #0f172a;
        }

        /* 背景帧2 - 左中蓝色光晕 + 右上紫色光晕 */
        .bg-frame-2 {
            background: 
                radial-gradient(circle at 30% 60%, rgba(6, 182, 212, 0.18) 0%, transparent 60%),
                radial-gradient(circle at 70% 20%, rgba(99, 102, 241, 0.12) 0%, transparent 60%),
                #0f172a;
        }

        /* 背景帧3 - 中心紫色光晕 + 左下蓝色光晕 */
        .bg-frame-3 {
            background: 
                radial-gradient(circle at 60% 40%, rgba(139, 92, 246, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 20% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 60%),
                #0f172a;
        }

        /* 背景帧4 - 右上紫色光晕 + 中下紫色光晕 */
        .bg-frame-4 {
            background: 
                radial-gradient(circle at 80% 30%, rgba(99, 102, 241, 0.18) 0%, transparent 60%),
                radial-gradient(circle at 40% 70%, rgba(139, 92, 246, 0.12) 0%, transparent 60%),
                #0f172a;
        }

        /* 背景帧5 - 左侧蓝色光晕 + 右侧紫色光晕 */
        .bg-frame-5 {
            background: 
                radial-gradient(circle at 10% 50%, rgba(6, 182, 212, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(99, 102, 241, 0.15) 0%, transparent 60%),
                #0f172a;
        }

        /* 背景帧6 - 多点渐变效果 */
        .bg-frame-6 {
            background: 
                radial-gradient(circle at 50% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 20% 70%, rgba(6, 182, 212, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                #0f172a;
        }

        /* 浮动粒子基础样式 */
        .particle {
            position: absolute; /* 绝对定位 */
            width: 3px;
            height: 3px;
            background: rgba(99, 102, 241, 0.6); /* 半透明紫色 */
            border-radius: 50%; /* 圆形 */
            animation: float 8s ease-in-out infinite; /* 浮动动画 */
            pointer-events: none; /* 不响应鼠标事件 */
        }

        /* 粒子变体样式 - 不同颜色和动画延迟 */
        .particle:nth-child(2n) {
            background: rgba(139, 92, 246, 0.5); /* 深紫色 */
            animation-delay: -2s; /* 动画延迟 */
            animation-duration: 10s; /* 动画持续时间 */
        }

        .particle:nth-child(3n) {
            background: rgba(6, 182, 212, 0.6); /* 蓝色 */
            animation-delay: -4s;
            animation-duration: 12s;
        }

        .particle:nth-child(4n) {
            background: rgba(99, 102, 241, 0.4); /* 浅紫色 */
            animation-delay: -6s;
            animation-duration: 9s;
        }

        /* 粒子浮动动画关键帧 */
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg); /* 起始和结束位置 */
                opacity: 0.3; /* 半透明 */
            }
            50% {
                transform: translateY(-30px) rotate(180deg); /* 中间位置上浮并旋转 */
                opacity: 0.8; /* 更不透明 */
            }
        }

        /* 内容区域样式 - 用于创建滚动空间 */
        .content {
            position: relative; /* 相对定位 */
            z-index: 1; /* 置于背景之上 */
            min-height: 500vh; /* 5倍视窗高度，创建足够的滚动空间 */
            padding: 2rem;
        }

        /* 内容区块样式 */
        .content-section {
            height: 100vh; /* 每个区块占满一屏 */
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 分隔线 */
        }

        /* 标题样式 */
        .content-section h2 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #6366f1, #8b5cf6); /* 渐变文字 */
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        /* 描述文字样式 */
        .content-section p {
            font-size: 1.2rem;
            color: rgba(248, 250, 252, 0.7);
            max-width: 600px;
            line-height: 1.8;
        }

        /* 滚动指示器样式 */
        .scroll-indicator {
            position: fixed;
            top: 50%;
            right: 2rem;
            transform: translateY(-50%);
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        /* 指示器点样式 */
        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        /* 激活状态的指示器点 */
        .indicator-dot.active {
            background: #6366f1;
            transform: scale(1.5);
        }

        /* 响应式设计 - 移动端适配 */
        @media (max-width: 768px) {
            .content-section h2 {
                font-size: 2rem;
            }
            
            .content-section p {
                font-size: 1rem;
                padding: 0 1rem;
            }
            
            .scroll-indicator {
                right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 动画背景容器 -->
    <div class="animated-background">
        <!-- 背景帧1 - 默认激活 -->
        <div class="bg-layer bg-frame-1 active"></div>
        <!-- 背景帧2 -->
        <div class="bg-layer bg-frame-2"></div>
        <!-- 背景帧3 -->
        <div class="bg-layer bg-frame-3"></div>
        <!-- 背景帧4 -->
        <div class="bg-layer bg-frame-4"></div>
        <!-- 背景帧5 -->
        <div class="bg-layer bg-frame-5"></div>
        <!-- 背景帧6 -->
        <div class="bg-layer bg-frame-6"></div>
        
        <!-- 浮动粒子 - 分布在不同位置 -->
        <div class="particle" style="left: 10%; top: 20%;"></div>
        <div class="particle" style="left: 20%; top: 60%;"></div>
        <div class="particle" style="left: 30%; top: 40%;"></div>
        <div class="particle" style="left: 40%; top: 80%;"></div>
        <div class="particle" style="left: 50%; top: 30%;"></div>
        <div class="particle" style="left: 60%; top: 70%;"></div>
        <div class="particle" style="left: 70%; top: 50%;"></div>
        <div class="particle" style="left: 80%; top: 25%;"></div>
        <div class="particle" style="left: 90%; top: 65%;"></div>
        <div class="particle" style="left: 15%; top: 75%;"></div>
        <div class="particle" style="left: 85%; top: 45%;"></div>
        <div class="particle" style="left: 25%; top: 85%;"></div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator">
        <div class="indicator-dot active" data-frame="0"></div>
        <div class="indicator-dot" data-frame="1"></div>
        <div class="indicator-dot" data-frame="2"></div>
        <div class="indicator-dot" data-frame="3"></div>
        <div class="indicator-dot" data-frame="4"></div>
        <div class="indicator-dot" data-frame="5"></div>
    </div>

    <!-- 页面内容区域 -->
    <div class="content">
        <!-- 第一屏内容 -->
        <div class="content-section">
            <div>
                <h2>背景帧 1</h2>
                <p>左上角紫色光晕配合右下角紫色光晕，营造温暖的视觉氛围</p>
            </div>
        </div>
        
        <!-- 第二屏内容 -->
        <div class="content-section">
            <div>
                <h2>背景帧 2</h2>
                <p>左中蓝色光晕与右上紫色光晕的对比，创造动态平衡感</p>
            </div>
        </div>
        
        <!-- 第三屏内容 -->
        <div class="content-section">
            <div>
                <h2>背景帧 3</h2>
                <p>中心紫色光晕搭配左下蓝色光晕，形成视觉焦点</p>
            </div>
        </div>
        
        <!-- 第四屏内容 -->
        <div class="content-section">
            <div>
                <h2>背景帧 4</h2>
                <p>右上与中下的紫色光晕呼应，营造稳定的视觉结构</p>
            </div>
        </div>
        
        <!-- 第五屏内容 -->
        <div class="content-section">
            <div>
                <h2>背景帧 5</h2>
                <p>左右两侧的蓝紫色光晕对称分布，创造平衡美感</p>
            </div>
        </div>
    </div>

    <script>
        /**
         * 背景动画控制器类
         * 负责管理背景帧的切换和粒子动画
         */
        class BackgroundAnimator {
            /**
             * 构造函数 - 初始化动画控制器
             */
            constructor() {
                // 获取所有背景帧元素
                this.frames = document.querySelectorAll('.bg-layer');
                // 获取所有指示器点元素
                this.indicators = document.querySelectorAll('.indicator-dot');
                // 当前显示的帧索引
                this.currentFrame = 0;
                // 背景帧总数
                this.totalFrames = this.frames.length;
                // 文档总高度（用于计算滚动进度）
                this.documentHeight = 0;
                // 初始化动画控制器
                this.init();
            }

            /**
             * 初始化方法 - 设置事件监听和初始状态
             */
            init() {
                // 更新文档高度
                this.updateDocumentHeight();
                // 绑定事件监听器
                this.bindEvents();
                // 更新背景显示
                this.updateBackground();
                
                console.log('背景动画控制器初始化完成');
                console.log(`总共 ${this.totalFrames} 个背景帧`);
            }

            /**
             * 更新文档高度 - 计算可滚动的总高度
             */
            updateDocumentHeight() {
                // 文档总高度减去视窗高度 = 可滚动高度
                this.documentHeight = document.documentElement.scrollHeight - window.innerHeight;
                console.log(`文档可滚动高度: ${this.documentHeight}px`);
            }

            /**
             * 绑定事件监听器
             */
            bindEvents() {
                // 监听滚动事件 - 实时更新背景
                window.addEventListener('scroll', () => this.updateBackground());
                
                // 监听窗口大小变化 - 重新计算高度
                window.addEventListener('resize', () => {
                    this.updateDocumentHeight();
                    this.updateBackground();
                });

                // 为指示器点添加点击事件
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => {
                        this.scrollToFrame(index);
                    });
                });
            }

            /**
             * 滚动到指定帧
             * @param {number} frameIndex - 目标帧索引
             */
            scrollToFrame(frameIndex) {
                // 计算目标滚动位置
                const targetScroll = (frameIndex / (this.totalFrames - 1)) * this.documentHeight;
                
                // 平滑滚动到目标位置
                window.scrollTo({
                    top: targetScroll,
                    behavior: 'smooth'
                });
                
                console.log(`滚动到帧 ${frameIndex + 1}, 位置: ${targetScroll}px`);
            }

            /**
             * 更新背景显示 - 根据滚动位置切换背景帧
             */
            updateBackground() {
                // 计算当前滚动进度 (0-1)
                const scrollProgress = Math.min(window.scrollY / this.documentHeight, 1);
                
                // 根据滚动进度计算应该显示的帧索引
                const frameIndex = Math.floor(scrollProgress * (this.totalFrames - 1));
                
                // 确保帧索引在有效范围内
                const clampedIndex = Math.max(0, Math.min(frameIndex, this.totalFrames - 1));

                // 如果帧发生变化，则切换显示
                if (clampedIndex !== this.currentFrame) {
                    this.switchFrame(clampedIndex);
                }

                // 更新粒子位置和透明度
                this.updateParticles(scrollProgress);
                
                // 更新指示器状态
                this.updateIndicators(clampedIndex);
            }

            /**
             * 切换背景帧
             * @param {number} newFrameIndex - 新的帧索引
             */
            switchFrame(newFrameIndex) {
                console.log(`切换背景帧: ${this.currentFrame + 1} -> ${newFrameIndex + 1}`);
                
                // 隐藏当前帧
                this.frames[this.currentFrame].classList.remove('active');
                
                // 显示新帧
                this.frames[newFrameIndex].classList.add('active');
                
                // 更新当前帧索引
                this.currentFrame = newFrameIndex;
            }

            /**
             * 更新粒子动画 - 根据滚动进度调整粒子位置和透明度
             * @param {number} scrollProgress - 滚动进度 (0-1)
             */
            updateParticles(scrollProgress) {
                const particles = document.querySelectorAll('.particle');
                
                particles.forEach((particle, index) => {
                    // 为每个粒子计算不同的基础延迟
                    const baseDelay = index * 0.2;
                    
                    // 基于滚动进度和三角函数计算Y轴偏移
                    const yOffset = Math.sin(scrollProgress * Math.PI * 3 + baseDelay) * 40;
                    
                    // 基于滚动进度和余弦函数计算X轴偏移
                    const xOffset = Math.cos(scrollProgress * Math.PI * 2 + baseDelay) * 25;
                    
                    // 计算透明度变化
                    const opacity = 0.2 + Math.abs(Math.sin(scrollProgress * Math.PI * 4 + baseDelay)) * 0.6;
                    
                    // 应用变换
                    particle.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
                    particle.style.opacity = opacity;
                });
            }

            /**
             * 更新指示器状态
             * @param {number} activeIndex - 当前激活的帧索引
             */
            updateIndicators(activeIndex) {
                this.indicators.forEach((indicator, index) => {
                    if (index === activeIndex) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.remove('active');
                    }
                });
            }
        }

        /**
         * 页面加载完成后初始化背景动画
         */
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，初始化背景动画控制器...');
            
            // 创建背景动画控制器实例
            const bgAnimator = new BackgroundAnimator();
            
            // 将控制器实例挂载到全局，便于调试
            window.bgAnimator = bgAnimator;
            
            console.log('背景逐帧滚动动画已启动！');
            console.log('使用方法：滚动页面查看背景帧切换效果');
            console.log('或点击右侧指示器快速跳转到指定帧');
        });
    </script>
</body>
</html>
