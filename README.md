# 滚动帧动画使用指南

## 📁 文件说明

- `scroll-animation.html` - 基础滚动动画（使用主题统一的图片）
- `frame-animation.html` - 帧序列动画（使用连续的图片帧）
- `README.md` - 使用说明文档

## 🎬 帧序列动画制作指南

### 1. 准备图片序列

#### 方法一：从视频提取帧
```bash
# 使用 FFmpeg 从视频提取帧
ffmpeg -i input_video.mp4 -vf fps=24 frame_%03d.jpg

# 参数说明：
# -i input_video.mp4  : 输入视频文件
# -vf fps=24         : 设置帧率为24fps
# frame_%03d.jpg     : 输出格式（frame_001.jpg, frame_002.jpg...）
```

#### 方法二：使用设计软件
- **After Effects**: 导出图片序列
- **Premiere Pro**: 导出帧
- **Photoshop**: 时间轴导出
- **Blender**: 渲染动画序列

#### 方法三：拍摄连续照片
- 使用相机连拍功能
- 保持相机位置固定
- 拍摄对象有连续变化

### 2. 图片命名规范

推荐使用以下命名格式：
```
frame_001.jpg
frame_002.jpg
frame_003.jpg
...
frame_024.jpg
```

### 3. 图片规格建议

- **分辨率**: 1920x1080 或更高
- **格式**: JPG（文件小）或PNG（质量高）
- **文件大小**: 每张图片控制在200KB以内
- **总数量**: 12-60帧（根据动画长度）

## 🔧 代码配置

### 修改帧数量
```javascript
const frameCount = 24; // 修改为您的图片数量
```

### 配置图片路径
```javascript
function generateImageUrls() {
    const urls = [];
    for (let i = 1; i <= frameCount; i++) {
        const frameNumber = i.toString().padStart(3, '0');
        urls.push(`./images/frame_${frameNumber}.jpg`); // 您的图片路径
    }
    return urls;
}
```

### 调整滚动区域
```css
.container {
    height: 600vh; /* 调整滚动距离，数值越大滚动越慢 */
}
```

## 📂 文件结构示例

```
项目文件夹/
├── frame-animation.html
├── images/
│   ├── frame_001.jpg
│   ├── frame_002.jpg
│   ├── frame_003.jpg
│   └── ...
└── README.md
```

## 🎨 动画效果类型

### 1. 时间推移动画
- 日出到日落
- 季节变化
- 云朵移动
- 城市变化

### 2. 物体运动动画
- 汽车行驶
- 人物走路
- 机械运转
- 液体流动

### 3. 变形动画
- 形状变化
- 颜色渐变
- 大小缩放
- 旋转效果

## ⚡ 性能优化建议

### 1. 图片优化
```bash
# 使用 ImageMagick 批量压缩
mogrify -resize 1920x1080 -quality 80 *.jpg
```

### 2. 预加载策略
- 优先加载前几帧
- 后台异步加载其余帧
- 显示加载进度

### 3. 内存管理
- 限制同时加载的图片数量
- 及时释放不需要的图片
- 使用懒加载技术

## 🌟 创意应用场景

1. **产品展示**: 360度产品旋转
2. **故事叙述**: 连续场景变化
3. **教学演示**: 步骤分解展示
4. **艺术创作**: 创意视觉效果
5. **品牌宣传**: 动态logo演示

## 🔍 常见问题

### Q: 图片加载慢怎么办？
A: 
- 压缩图片文件大小
- 使用CDN加速
- 实现渐进式加载

### Q: 动画不够流畅？
A: 
- 增加帧数
- 减少transition时间
- 使用GPU加速

### Q: 移动端适配问题？
A: 
- 准备不同尺寸的图片
- 使用响应式图片
- 优化移动端性能

## 📱 移动端优化

```css
/* 移动端优化 */
@media (max-width: 768px) {
    .frame {
        background-size: cover;
        background-attachment: scroll; /* 避免iOS问题 */
    }
}
```

## 🚀 进阶功能

1. **添加音效**: 配合帧动画播放音效
2. **交互控制**: 鼠标悬停暂停/播放
3. **循环播放**: 到达末尾自动重新开始
4. **速度控制**: 动态调整播放速度
5. **方向控制**: 支持反向播放

---

**提示**: 开始时建议使用较少的帧数（12-24帧）进行测试，确认效果满意后再增加帧数。
